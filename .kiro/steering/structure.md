# Project Structure

## Root Directory Layout
```
albatross-kiotviet-mcp/
├── .env                    # Environment variables (not in git)
├── .env.example           # Environment template
├── .gitignore             # Git ignore rules
├── README.md              # Main documentation
├── CHANGELOG.md           # Version history
├── TROUBLESHOOTING.md     # Common issues and solutions
├── pyproject.toml         # Python project configuration
├── requirements.txt       # Python dependencies
├── uv.lock               # UV lock file for reproducible builds
├── mcp.json              # MCP server configuration
├── example_usage.py      # Usage examples and testing
├── monitor_logs.py       # Log monitoring utility
├── test_mcp_server.py    # Server functionality tests
├── test_mcp_tools.py     # MCP tools tests
├── logs/                 # Application logs (auto-created)
└── src/                  # Source code
```

## Source Code Organization (Clean Architecture)
```
src/albatross_kiotviet_mcp/
├── __init__.py                    # Package initialization
├── main.py                        # Application entry point
├── server.py                      # FastMCP server setup
├── domain/                        # Business logic and entities
│   ├── entities/                  # Domain entities
│   └── interfaces/                # Abstract interfaces
├── infrastructure/                # External concerns
│   ├── api/                       # KiotViet API client
│   ├── auth/                      # Authentication
│   └── config/                    # Configuration
├── application/                   # Use cases and services
│   └── services/                  # Business services
└── tools/                         # Individual MCP tools
    ├── base/                      # Base tool classes
    ├── categories/                # Categories tool
    ├── products/                  # Products tool
    └── branches/                  # Branches tool
```

## Key Components

### Clean Architecture Layers
- **Domain Layer**: Business entities and interfaces (domain/)
- **Infrastructure Layer**: External concerns like API, auth, config (infrastructure/)
- **Application Layer**: Business services and use cases (application/)
- **Tools Layer**: Individual MCP tools with clear responsibilities (tools/)

### Core Modules
- **main.py**: Application entry point with logging setup
- **server.py**: FastMCP server setup and tool registration
- **infrastructure/api/**: HTTP client for KiotViet API with retry logic
- **infrastructure/auth/**: OAuth2 token management with automatic refresh
- **infrastructure/config/**: Pydantic-based configuration with environment variables

### Configuration Files
- **.env**: Local environment variables (never commit)
- **.env.example**: Template for required environment variables
- **pyproject.toml**: Python packaging, dependencies, and metadata
- **mcp.json**: MCP server configuration for Claude Desktop

### Testing & Examples
- **example_usage.py**: Direct API client usage examples
- **test_mcp_server.py**: Server functionality testing
- **test_mcp_tools.py**: MCP tool testing
- **monitor_logs.py**: Log file monitoring utility

## File Naming Conventions
- **Snake_case**: All Python files and modules
- **Lowercase**: Configuration files (.env, pyproject.toml)
- **UPPERCASE**: Documentation files (README.md, CHANGELOG.md)
- **Descriptive names**: Files clearly indicate their purpose

## Directory Conventions
- **src/**: All source code under src layout
- **logs/**: Auto-created for application logs
- **.venv/**: Virtual environment (not in git)
- **__pycache__/**: Python cache directories (not in git)

## Import Structure
- **Clean Architecture imports**: Use full paths (`from .infrastructure.config.settings import get_config`)
- **Absolute imports**: External packages (`from fastmcp import FastMCP`)
- **Standard library**: First, then third-party, then local imports
- **Layer separation**: Each layer imports only from lower layers (domain ← infrastructure ← application ← tools)

## Clean Architecture Benefits
- **Separation of Concerns**: Each layer has a single responsibility
- **Dependency Inversion**: High-level modules don't depend on low-level modules
- **Testability**: Easy to mock dependencies and test individual components
- **Maintainability**: Changes in one layer don't affect others
- **Extensibility**: Easy to add new tools and features

## Logging Structure
- **Daily log files**: `logs/kiotviet_mcp_YYYYMMDD.log`
- **Structured format**: Timestamp, logger name, level, message
- **Emoji indicators**: For easy visual scanning of log levels
- **Both file and console**: Dual output for development and production