# KiotViet MCP Server

A professional Model Context Protocol (MCP) server for integrating with KiotViet Public API. This server enables AI assistants like <PERSON> to interact with KiotViet's retail management system through natural language queries.

## Features

- **🏗️ Clean Architecture**: Domain-driven design with clear separation of concerns
- **🔐 Secure Authentication**: Automatic OAuth2 token management with refresh handling
- **🛠️ MCP Tools**: Categories, products, and branches retrieval with pagination
- **🔒 Type Safety**: Complete type hints with Pydantic validation
- **🧪 Comprehensive Testing**: Unit and integration tests with real API validation
- **⚡ Production Ready**: Error handling, logging, and async/await throughout
- **🔧 Extensible**: Easy-to-follow patterns for adding new tools

## Project Structure

This project follows **Clean Architecture** principles with clear separation of concerns:

```
src/albatross_kiotviet_mcp/
├── domain/                        # Business logic layer
│   ├── entities/                  # Domain entities (Category, Product, Branch)
│   │   ├── category.py           # Category entity with Pydantic validation
│   └── interfaces/               # Abstract interfaces
│       └── api_client.py         # IKiotVietAPIClient interface
├── infrastructure/               # External concerns layer
│   ├── api/                      # API client implementations
│   │   ├── base_client.py        # Base HTTP client with auth
│   │   └── kiotviet_client.py    # KiotViet-specific API client
│   ├── auth/                     # Authentication management
│   │   └── token_manager.py     # OAuth2 token lifecycle
│   └── config/                   # Configuration management
│       └── settings.py           # Pydantic settings with env vars
├── tools/                        # MCP tools layer
│   ├── base/                     # Base tool classes
│   │   └── base_tool.py          # BaseMCPTool abstract class
│   └── categories/               # Categories tool implementation
│       └── categories_tool.py    # CategoriesTool with validation
├── application/                  # Application services layer
│   └── services/                 # Business services (future)
├── main.py                       # Application entry point
└── server.py                     # FastMCP server setup and tool registration
```

### Architecture Benefits

- **Domain Layer**: Pure business logic, no external dependencies
- **Infrastructure Layer**: Handles external APIs, configuration, authentication
- **Tools Layer**: MCP-specific implementations with clean interfaces
- **Application Layer**: Orchestrates business workflows (extensible)

## Getting Started

### Prerequisites

- **Python 3.10+**
- **KiotViet API Credentials**: Client ID, Client Secret, Retailer name
- **uv** (recommended) or pip for dependency management

### 1. Environment Setup

```bash
# Clone the repository
git clone <repository-url>
cd albatross-kiotviet-mcp

# Install dependencies with uv (recommended)
uv sync

# Or with pip
pip install -e .
```

### 2. Configuration

Create a `.env` file with your KiotViet credentials:

```env
KIOTVIET_CLIENT_ID=your_client_id_here
KIOTVIET_CLIENT_SECRET=your_client_secret_here
KIOTVIET_RETAILER=your_retailer_name_here
```

### 3. Run the MCP Server

```bash
# Using uv (recommended)
uv run albatross-kiotviet-mcp-server

# Or using the installed script
albatross-kiotviet-mcp-server

# Development mode with auto-reload
uv run python -m albatross_kiotviet_mcp.main
```

### 4. Connect from Claude Desktop

Edit your Claude Desktop configuration file:

**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "kiotviet": {
      "command": "uv",
      "args": [
        "--directory",
        "/path/to/albatross-kiotviet-mcp",
        "run",
        "albatross-kiotviet-mcp-server"
      ],
      "env": {
        "PYTHONUNBUFFERED": "1"
      }
    }
  }
}
```

Restart Claude Desktop to load the configuration.

## Available Tools

The server currently provides **3 MCP tools** for interacting with KiotViet:

### `get_categories`
Retrieve product categories with pagination and hierarchical support.

**Parameters:**
- `page_size` (int, optional): Items per page (1-100, default: 50)
- `current_item` (int, optional): Starting index (default: 0)
- `order_direction` (str, optional): "Asc" or "Desc" (default: "Asc")
- `hierarchical_data` (bool, optional): Return hierarchical structure (default: False)

**Example Usage in Claude:**
```
Get all product categories from KiotViet
Show me categories in descending order with hierarchical structure
```

## Development Guide

### Adding New MCP Tools

Follow these steps to add a new tool (e.g., `get_customers`):

#### 1. Create the Tool Class

Create `src/albatross_kiotviet_mcp/tools/customers/customers_tool.py`:

```python
"""Customers MCP tool implementation."""

from typing import Dict, Any, Callable
from ..base.base_tool import BaseMCPTool
from ...domain.interfaces.api_client import IKiotVietAPIClient

class CustomersTool(BaseMCPTool):
    """MCP tool for retrieving customers from KiotViet."""

    def __init__(self, api_client: IKiotVietAPIClient):
        super().__init__(
            name="get_customers",
            description="Get customers from KiotViet API with pagination support"
        )
        self.api_client = api_client

    async def execute(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc"
    ) -> Dict[str, Any]:
        """Execute the customers tool."""
        try:
            # Validate parameters
            self.pre_execute(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction
            )

            # Make API call
            async with self.api_client:
                result = await self.api_client.get_customers(
                    page_size=page_size,
                    current_item=current_item,
                    order_direction=order_direction
                )

            return result.model_dump()

        except Exception as e:
            self.logger.error(f"Failed to retrieve customers: {e}")
            raise

    def pre_execute(self, **kwargs) -> None:
        """Validate customers tool parameters."""
        page_size = kwargs.get('page_size', 50)
        current_item = kwargs.get('current_item', 0)
        order_direction = kwargs.get('order_direction', 'Asc')

        if not isinstance(page_size, int) or page_size < 1 or page_size > 100:
            raise ValueError("page_size must be an integer between 1 and 100")

        if not isinstance(current_item, int) or current_item < 0:
            raise ValueError("current_item must be a non-negative integer")

        if order_direction not in ['Asc', 'Desc']:
            raise ValueError("order_direction must be 'Asc' or 'Desc'")

    def _create_wrapper_function(self) -> Callable:
        """Create wrapper function for FastMCP registration."""
        async def get_customers(
            page_size: int = 50,
            current_item: int = 0,
            order_direction: str = "Asc"
        ) -> Dict[str, Any]:
            """Get customers from KiotViet API."""
            return await self.execute(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction
            )
        return get_customers
```

#### 2. Add API Client Method

Add the method to `src/albatross_kiotviet_mcp/infrastructure/api/kiotviet_client.py`:

```python
async def get_customers(
    self,
    page_size: int = 50,
    current_item: int = 0,
    order_direction: str = "Asc"
) -> CustomerResponse:
    """Get customers from KiotViet API."""
    data = {
        "pageSize": page_size,
        "currentItem": current_item,
        "orderDirection": order_direction
    }

    raw_response = await self.make_request("GET", "/customers", data=data)
    return CustomerResponse.model_validate(raw_response)
```

#### 3. Register with FastMCP Server

Update `src/albatross_kiotviet_mcp/server.py`:

```python
from .tools.customers.customers_tool import CustomersTool

# Add global instance
_customers_tool: Optional[CustomersTool] = None

def get_customers_tool() -> CustomersTool:
    """Get or create customers tool instance."""
    global _customers_tool
    if _customers_tool is None:
        client = get_kiotviet_client()
        _customers_tool = CustomersTool(client)
    return _customers_tool

def register_tools():
    """Register all MCP tools with the FastMCP server."""
    # ... existing tools ...

    # Register customers tool
    customers_tool = get_customers_tool()
    server.tool(
        name=customers_tool.name,
        description=customers_tool.description
    )(customers_tool.get_wrapper_function())
```

#### 4. Create Domain Entity

Create `src/albatross_kiotviet_mcp/domain/entities/customer.py`:

```python
"""Customer domain entity."""

from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict

class Customer(BaseModel):
    """KiotViet customer entity."""

    customer_id: int = Field(alias="customerId", description="Customer ID")
    customer_name: str = Field(alias="customerName", description="Customer name")
    # ... other fields

    model_config = ConfigDict(from_attributes=True)

class CustomerResponse(BaseModel):
    """Response model for customer API calls."""

    total: int = Field(description="Total number of customers")
    page_size: int = Field(alias="pageSize", description="Number of items per page")
    data: List[Customer] = Field(description="List of customers")

    model_config = ConfigDict(from_attributes=True)
```

#### 5. Write Tests

Create `tests/test_customers_tool.py`:

```python
"""Tests for customers tool."""

import pytest
from unittest.mock import AsyncMock
from src.albatross_kiotviet_mcp.tools.customers.customers_tool import CustomersTool

class TestCustomersTool:
    """Test cases for CustomersTool."""

    @pytest.fixture
    def customers_tool(self, mock_api_client):
        """Create customers tool instance for testing."""
        return CustomersTool(mock_api_client)

    @pytest.mark.asyncio
    async def test_execute_with_defaults(self, customers_tool, mock_api_client):
        """Test customers tool execution with default parameters."""
        # Mock response
        mock_response = {"total": 100, "pageSize": 50, "data": []}
        mock_api_client.get_customers.return_value.model_dump.return_value = mock_response

        result = await customers_tool.execute()

        assert result == mock_response
        mock_api_client.get_customers.assert_called_once_with(
            page_size=50,
            current_item=0,
            order_direction='Asc'
        )
```

### Testing Strategy

#### Unit Tests (Fast, Isolated)
```bash
# Run unit tests with mocks
uv run pytest tests/test_customers_tool.py::TestCustomersTool -v
```

#### Integration Tests (Real API)
```bash
# Run integration tests with real API calls
uv run pytest tests/test_customers_tool.py::TestCustomersToolIntegration -v -m integration
```

#### Debug Scripts
Create debug scripts for step-by-step testing:
```bash
# Create debug_customers_tool.py for manual debugging
uv run python debug_customers_tool.py
```

### Required Methods for New Tools

Every MCP tool must implement these methods from `BaseMCPTool`:

1. **`async def execute(self, **kwargs) -> Dict[str, Any]`**: Main tool logic
2. **`def pre_execute(self, **kwargs) -> None`**: Parameter validation
3. **`def _create_wrapper_function(self) -> Callable`**: FastMCP wrapper function

## Testing & Development

### Running Tests

```bash
# Install test dependencies
uv sync --extra test

# Run all tests
uv run pytest

# Run unit tests only (fast, with mocks)
uv run pytest tests/ -k "not integration"

# Run integration tests (real API calls)
uv run pytest tests/ -m integration

# Run with coverage
uv run pytest --cov=src --cov-report=html

# Run specific test file
uv run pytest tests/test_categories_tool.py -v
```

### Code Quality

```bash
# Install development dependencies
uv sync --extra dev

# Format code
uv run black src tests

# Sort imports
uv run isort src tests

# Lint code
uv run flake8 src tests

# Type checking
uv run mypy src
```

### Debug Scripts

For step-by-step debugging with real API calls:

```bash
# Debug categories tool
uv run python debug_categories_tool.py

# Create similar debug scripts for new tools
cp debug_categories_tool.py debug_customers_tool.py
```

## Troubleshooting

### Common Issues

**Server not starting:**
- Verify `.env` file contains all required credentials
- Check KiotViet API credentials are valid
- Review logs for specific error messages

**Authentication errors:**
- Confirm `KIOTVIET_CLIENT_ID` and `KIOTVIET_CLIENT_SECRET` are correct
- Ensure KiotViet account has API access enabled
- Verify `KIOTVIET_RETAILER` matches your account name

**Claude Desktop integration:**
- Check the path in `claude_desktop_config.json` is absolute and correct
- Restart Claude Desktop after configuration changes
- Look for MCP connection errors in Claude's logs

### Logging

Logs are written to `logs/kiotviet_mcp_YYYYMMDD.log` with detailed information about:
- Server startup and configuration
- API requests and responses
- Authentication token lifecycle
- Tool execution and errors

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-tool`
3. Follow the development guide for adding new tools
4. Add comprehensive tests (unit + integration)
5. Ensure code quality checks pass
6. Submit a pull request

## License

MIT License - see LICENSE file for details.