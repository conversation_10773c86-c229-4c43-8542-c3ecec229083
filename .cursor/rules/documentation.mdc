# Documentation (MANDATORY)

## Documentation Standards (REQUIRED)
- Module docstring at top of every file
- Class docstring for every class
- Method docstring for every public method
- Include Args, Returns, and Raises sections

```python
"""Module docstring describing purpose."""

class ExampleClass:
    """Class docstring explaining purpose and usage."""
    
    def public_method(self, param: str) -> str:
        """Method docstring.
        
        Args:
            param: Description of parameter
            
        Returns:
            Description of return value
            
        Raises:
            ValueError: When parameter is invalid
        """
        pass
```

## Pydantic Model Documentation
- Add descriptive docstrings to all Pydantic models
- Use Field() descriptions for all fields
- Include example usage where appropriate

```python
class Category(BaseModel):
    """KiotViet category entity representing a product category.
    
    This model maps to the KiotViet API category response structure.
    """
    
    category_id: int = Field(alias="categoryId", description="Unique category identifier")
    category_name: str = Field(alias="categoryName", description="Human-readable category name")
    
    model_config = ConfigDict(from_attributes=True)
```

## Forbidden Patterns

### ❌ NEVER DO THESE:
- Missing docstrings on public APIs
- Missing module docstrings
- Missing class docstrings
- Missing method docstrings
- Missing parameter documentation
description:
globs:
alwaysApply: false
---
