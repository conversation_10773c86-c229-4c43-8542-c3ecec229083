# Type Safety Standards (MANDATORY)

## Type Annotations (REQUIRED)
- ALL functions and methods must have complete type annotations
- Use typing imports: `from typing import Dict, Any, Optional, List, Union, Callable`
- Return types are mandatory for all functions
- Parameter types are mandatory for all parameters

```python
# ✅ CORRECT
async def get_categories(
    self,
    page_size: int = 50,
    current_item: int = 0
) -> CategoryResponse:
    pass

# ❌ INCORRECT - Missing type hints
async def get_categories(self, page_size=50):
    pass
```

## Pydantic Models (REQUIRED FOR ALL DATA)
- Use Pydantic BaseModel for all domain entities
- Use Field() with alias for API field mapping
- Include ConfigDict(from_attributes=True)
- Add descriptive docstrings

```python
# ✅ CORRECT
class Category(BaseModel):
    """KiotViet category entity."""
    
    category_id: int = Field(alias="categoryId", description="Category ID")
    category_name: str = Field(alias="categoryName", description="Category name")
    
    model_config = ConfigDict(from_attributes=True)
```

## Import Organization (STRICT ORDER)
1. Standard library imports
2. Third-party imports  
3. Local imports (relative)

```python
# ✅ CORRECT ORDER
import asyncio
import logging
from typing import Dict, Any, Optional

import httpx
from pydantic import BaseModel, Field

from ..base.base_tool import BaseMCPTool
from ...domain.interfaces.api_client import IKiotVietAPIClient
```

## Forbidden Patterns

### ❌ NEVER DO THESE:
- Missing type hints on functions/methods
- Missing return type annotations
- Missing parameter type annotations
- Incorrect import order
- Missing Pydantic models for data structures
description:
globs:
alwaysApply: false
---
