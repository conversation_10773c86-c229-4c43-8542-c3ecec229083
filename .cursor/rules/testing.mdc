# Testing Rules

## Test Organization (REQUIRED)
- Unit tests: `tests/test_{tool_name}_tool.py` (with mocks)
- Integration tests: `tests/integration/test_{tool_name}_integration.py` (real API)
- Use pytest fixtures for setup
- Mark integration tests with `@pytest.mark.integration`

## Test Patterns (ENFORCED)
```python
# Unit test pattern
class TestToolName:
    @pytest.fixture
    def tool_instance(self, mock_api_client):
        return ToolName(mock_api_client)
    
    @pytest.mark.asyncio
    async def test_execute_success(self, tool_instance):
        # Test with mocks
        pass

# Integration test pattern  
class TestToolNameIntegration:
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_real_api_call(self):
        # Test with real API
        pass
```

## Test Requirements
- Unit tests must use mocks for external dependencies
- Integration tests must test real API interactions
- All public methods must have test coverage
- Use pytest fixtures for common setup
- Test both success and error scenarios

## Forbidden Patterns

### ❌ NEVER DO THESE:
- Tests without proper mocking/real API separation
- Missing test coverage for public methods
- Missing error scenario tests
- Missing async test decorators
- Missing integration test markers
description:
globs:
alwaysApply: false
---
