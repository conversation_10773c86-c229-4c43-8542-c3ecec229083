# Code Review Checklist

Before committing, verify:

## Type Safety
- [ ] Complete type hints on all functions
- [ ] Pydantic models for all data structures  
- [ ] Proper return type annotations
- [ ] Parameter type annotations

## Architecture
- [ ] Clean Architecture layer separation
- [ ] No circular imports
- [ ] Proper dependency injection
- [ ] Layer boundaries respected

## Async & Resources
- [ ] Async context managers for resources
- [ ] Proper resource cleanup
- [ ] No resource leaks
- [ ] Async/await patterns followed

## Error Handling
- [ ] Specific exception handling with logging
- [ ] Meaningful error messages
- [ ] Proper error context
- [ ] No silent exceptions

## Documentation
- [ ] Comprehensive docstrings
- [ ] Module docstrings
- [ ] Class docstrings
- [ ] Method docstrings with Args/Returns/Raises

## Testing
- [ ] Unit tests with mocks
- [ ] Integration tests for API validation
- [ ] Test coverage for public methods
- [ ] Error scenario tests

## Code Quality
- [ ] Proper import organization
- [ ] Consistent naming conventions
- [ ] No hardcoded values
- [ ] Logging with appropriate levels

## Performance
- [ ] Efficient async operations
- [ ] Proper connection pooling
- [ ] No blocking operations in async code
- [ ] Resource cleanup implemented

## Security
- [ ] No sensitive data in code
- [ ] Proper environment variable usage
- [ ] Input validation implemented
- [ ] Error messages don't leak sensitive info
description:
globs:
alwaysApply: false
---
