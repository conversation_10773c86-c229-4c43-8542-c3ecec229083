# Async/Await Patterns (MANDATORY)

## Async Context Managers (REQUIRED)
- Always use async context managers for resources
- Use `async with` for API clients
- Proper resource cleanup in __aexit__

```python
# ✅ CORRECT
async def execute(self) -> Dict[str, Any]:
    async with self.api_client:
        result = await self.api_client.get_categories()
    return result.model_dump()
```

## Async Best Practices (ENFORCED)
- Use async/await for all I/O operations
- Implement proper connection pooling
- Use context managers for resource cleanup
- Add retry logic with exponential backoff

## Resource Management (MANDATORY)
- Always use async context managers
- Proper cleanup in __aexit__ methods
- No resource leaks

## Forbidden Patterns

### ❌ NEVER DO THESE:
- Direct API calls without context managers
- Resource leaks (missing cleanup)
- Synchronous I/O in async functions
- Missing async context managers for resources
description:
globs:
alwaysApply: false
---
