# KiotViet MCP Project - Architecture Rules

## Clean Architecture Layers (STRICT ENFORCEMENT)
- **Domain Layer**: Pure business logic, no external dependencies
- **Infrastructure Layer**: External concerns (API, auth, config)
- **Tools Layer**: MCP-specific implementations
- **Application Layer**: Business services and orchestration

### Dependency Rules
- Domain layer cannot import from Infrastructure/Tools/Application
- Infrastructure can only import from Domain interfaces
- Tools can import from Domain and Infrastructure
- Application orchestrates all layers

### File Organization Structure
```
src/albatross_kiotviet_mcp/
├── domain/entities/{entity_name}.py      # One entity per file
├── domain/interfaces/{interface_name}.py # One interface per file
├── infrastructure/api/                   # API clients
├── infrastructure/auth/                  # Authentication
├── infrastructure/config/                # Configuration
├── tools/{tool_name}/{tool_name}_tool.py # One tool per directory
└── application/services/                 # Business services
```

### Naming Conventions (ENFORCED)
- Files: snake_case.py
- Classes: PascalCase
- Functions/Methods: snake_case
- Constants: UPPER_SNAKE_CASE
- Private methods: _private_method

## Forbidden Patterns

### ❌ NEVER DO THESE:
- Circular imports between layers
- Domain layer importing from Infrastructure/Tools/Application
- Infrastructure importing from Tools/Application
- Missing layer separation
description:
globs:
alwaysApply: false
---
