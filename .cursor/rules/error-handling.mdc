# Error Handling (SPECIFIC EXCEPTIONS ONLY)

## Exception Handling (REQUIRED)
- Use specific exception types, not generic Exception
- Always log errors with context
- Provide meaningful error messages

```python
# ✅ CORRECT
try:
    result = await self.api_client.get_categories()
except ValueError as e:
    self.logger.error(f"Invalid parameters: {e}")
    raise CustomToolError(f"Invalid parameters: {str(e)}")
except httpx.HTTPStatusError as e:
    self.logger.error(f"API error {e.response.status_code}: {e}")
    raise CustomToolError(f"API request failed: {str(e)}")
```

## Structured Logging (REQUIRED)
- Use logger = logging.getLogger(__name__) in every module
- Log levels: debug, info, warning, error
- Include context in log messages

```python
logger = logging.getLogger(__name__)

# ✅ CORRECT LOGGING
logger.info(f"Executing {self.name} with params: {kwargs}")
logger.error(f"API error {status_code}: {error_message}")
```

## Forbidden Patterns

### ❌ NEVER DO THESE:
- Generic Exception handling without specific types
- Missing error logging
- Silent exception handling
- Missing context in error messages
description:
globs:
alwaysApply: false
---
