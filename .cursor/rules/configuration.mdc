# Configuration Rules

## Pydantic Settings (MANDATORY)
- Use BaseSettings for all configuration
- Environment variable aliases with <PERSON>(alias="ENV_VAR")
- Include SettingsConfigDict with .env file support

```python
class NewConfig(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8"
    )
    
    required_field: str = Field(alias="ENV_VAR_NAME")
    optional_field: int = Field(default=30, alias="ENV_VAR_OPTIONAL")
```

## Configuration Best Practices
- Use descriptive field names
- Provide meaningful default values
- Include field descriptions
- Use proper type annotations
- Validate configuration on startup

## Environment Variables
- Use UPPER_SNAKE_CASE for environment variable names
- Provide clear documentation for each variable
- Use appropriate field types (str, int, bool, etc.)
- Include validation where appropriate

## Forbidden Patterns

### ❌ NEVER DO THESE:
- Hardcoded configuration values
- Missing environment variable aliases
- Missing field descriptions
- Missing type annotations
- Missing validation
description:
globs:
alwaysApply: false
---
