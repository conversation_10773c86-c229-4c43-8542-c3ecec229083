# MCP Tool Implementation Rules

## BaseMCPTool Pattern (MANDATORY)
Every MCP tool must inherit from BaseMCPTool and implement:

1. `__init__` with proper super() call
2. `async def execute(**kwargs) -> Dict[str, Any]`
3. `def pre_execute(**kwargs) -> None` for validation
4. `def _create_wrapper_function() -> Callable` for FastMCP

```python
# ✅ REQUIRED PATTERN
class NewTool(BaseMCPTool):
    def __init__(self, api_client: IKiotVietAPIClient):
        super().__init__(name="tool_name", description="Tool description")
        self.api_client = api_client
    
    async def execute(self, **kwargs) -> Dict[str, Any]:
        try:
            self.pre_execute(**kwargs)
            async with self.api_client:
                typed_result = await self.api_client.method_name(**kwargs)
            return typed_result.model_dump()
        except ValueError as e:
            self.logger.error(f"Parameter validation failed: {e}")
            raise CustomToolError(f"Invalid parameters: {str(e)}")
    
    def pre_execute(self, **kwargs) -> None:
        # Parameter validation logic
        pass
    
    def _create_wrapper_function(self) -> Callable:
        async def wrapper(**kwargs) -> Dict[str, Any]:
            return await self.execute(**kwargs)
        return wrapper
```

## API Client Pattern (REQUIRED)
- All API methods must return typed Pydantic responses
- Use make_request() from base client
- Validate responses with model_validate()

```python
# ✅ CORRECT API CLIENT METHOD
async def get_categories(
    self,
    page_size: int = 50,
    current_item: int = 0
) -> CategoryResponse:
    data = {"pageSize": page_size, "currentItem": current_item}
    raw_response = await self.make_request("GET", "/categories", data=data)
    return CategoryResponse.model_validate(raw_response)
```

## Forbidden Patterns

### ❌ NEVER DO THESE:
- Missing BaseMCPTool inheritance
- Missing execute() method implementation
- Missing pre_execute() validation
- Missing wrapper function creation
- Direct API calls without proper patterns
description:
globs:
alwaysApply: false
---
