# KiotViet MCP Project Overview

## Project Context
This is a Model Context Protocol (MCP) server for KiotViet API integration using Clean Architecture principles. The project enables AI assistants to interact with KiotViet's retail management system.

## Key Project Files

### Configuration
- [pyproject.toml](mdc:pyproject.toml) - Project configuration and dependencies
- [mcp.json](mdc:mcp.json) - MCP server configuration
- [requirements.txt](mdc:requirements.txt) - Python dependencies

### Documentation
- [README.md](mdc:README.md) - Project documentation and setup guide
- [CODING_STANDARDS.md](mdc:CODING_STANDARDS.md) - Detailed coding standards
- [CHANGELOG.md](mdc:CHANGELOG.md) - Version history and changes

### Build & Deployment
- [Dockerfile](mdc:Dockerfile) - Container configuration
- [docker-compose.yml](mdc:docker-compose.yml) - Local development setup
- [Makefile](mdc:Makefile) - Build and development commands

### Source Code Structure
- [src/albatross_kiotviet_mcp/main.py](mdc:src/albatross_kiotviet_mcp/main.py) - Application entry point
- [src/albatross_kiotviet_mcp/server.py](mdc:src/albatross_kiotviet_mcp/server.py) - MCP server implementation

### Domain Layer
- [src/albatross_kiotviet_mcp/domain/entities/category.py](mdc:src/albatross_kiotviet_mcp/domain/entities/category.py) - Category domain entity
- [src/albatross_kiotviet_mcp/domain/interfaces/api_client.py](mdc:src/albatross_kiotviet_mcp/domain/interfaces/api_client.py) - API client interface

### Infrastructure Layer
- [src/albatross_kiotviet_mcp/infrastructure/api/base_client.py](mdc:src/albatross_kiotviet_mcp/infrastructure/api/base_client.py) - Base API client
- [src/albatross_kiotviet_mcp/infrastructure/api/kiotviet_client.py](mdc:src/albatross_kiotviet_mcp/infrastructure/api/kiotviet_client.py) - KiotViet API client
- [src/albatross_kiotviet_mcp/infrastructure/config/settings.py](mdc:src/albatross_kiotviet_mcp/infrastructure/config/settings.py) - Application settings

### Tools Layer
- [src/albatross_kiotviet_mcp/tools/base/base_tool.py](mdc:src/albatross_kiotviet_mcp/tools/base/base_tool.py) - Base MCP tool implementation
- [src/albatross_kiotviet_mcp/tools/categories/categories_tool.py](mdc:src/albatross_kiotviet_mcp/tools/categories/categories_tool.py) - Categories MCP tool

## Architecture Overview
The project follows Clean Architecture with four main layers:
1. **Domain**: Pure business logic and entities
2. **Infrastructure**: External concerns (API, auth, config)
3. **Tools**: MCP-specific implementations
4. **Application**: Business services and orchestration

## Development Workflow
1. Follow the coding standards in [CODING_STANDARDS.md](mdc:CODING_STANDARDS.md)
2. Use the code review checklist for quality assurance
3. Implement both unit and integration tests
4. Follow async patterns and proper resource management
5. Maintain Clean Architecture layer separation
description:
globs:
alwaysApply: false
---
