"""Base MCP tool class."""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Callable
import logging

logger = logging.getLogger(__name__)


class BaseMCPTool(ABC):
    """Base class for all MCP tools."""

    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.logger = logging.getLogger(f"{__name__}.{name}")
        self._wrapper_function: Optional[Callable] = None

    @abstractmethod
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute the tool with given parameters.

        Args:
            **kwargs: Tool-specific parameters

        Returns:
            Tool execution result
        """
        pass

    @abstractmethod
    def pre_execute(self, **kwargs) -> None:
        """Validate tool parameters before execution.

        Args:
            **kwargs: Parameters to validate

        Raises:
            ValueError: If parameters are invalid
        """
        pass

    def get_wrapper_function(self) -> Callable:
        """Get a wrapper function that can be registered with FastMCP.

        This creates a function with the proper signature that FastMCP can introspect
        and register as a tool.

        Returns:
            Callable function that wraps this tool's execute method
        """
        if self._wrapper_function is None:
            self._wrapper_function = self._create_wrapper_function()
        return self._wrapper_function

    def _create_wrapper_function(self) -> Callable:
        """Create the wrapper function with proper signature.

        This method should be overridden by subclasses to provide the correct
        function signature that matches their execute method parameters.

        Returns:
            Callable function with proper signature
        """
        # Default implementation - subclasses should override this
        async def wrapper(**kwargs) -> Dict[str, Any]:
            """Default wrapper function."""
            return await self.execute(**kwargs)

        # Set function metadata
        wrapper.__name__ = self.name
        wrapper.__doc__ = self.description

        return wrapper