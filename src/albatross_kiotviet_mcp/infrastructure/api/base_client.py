"""Base KiotViet API client with shared implementation."""

from abc import ABC
from typing import Dict, Any, Optional
import asyncio
import httpx
import logging
from ...domain.interfaces.api_client import IKiotVietAPIClient
from ...infrastructure.config.settings import KiotVietConfig
from ...infrastructure.auth.token_manager import TokenManager

logger = logging.getLogger(__name__)


class BaseKiotVietClient(IKiotVietAPIClient, ABC):
    """Base class for KiotViet API client with shared implementation.
    
    This class provides common functionality like authentication, request handling,
    retry logic, and error handling that can be reused by concrete implementations.
    """
    
    def __init__(self, config: KiotVietConfig):
        """Initialize the base client.
        
        Args:
            config: KiotViet configuration
        """
        self.config = config
        self.token_manager = TokenManager(config)
        self._client: Optional[httpx.AsyncClient] = None
    
    async def __aenter__(self):
        """Initialize HTTP client and authenticate."""
        self._client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.config.request_timeout),
            limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Clean up HTTP client."""
        if self._client:
            await self._client.aclose()
            self._client = None
    
    async def _get_headers(self) -> Dict[str, str]:
        """Get headers with authentication token."""
        access_token = await self.token_manager.get_access_token()
        return {
            "Retailer": self.config.retailer,
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
    
    async def make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Make an authenticated request to KiotViet API.
        
        This is the shared implementation that handles:
        - Authentication with automatic token refresh
        - Retry logic with exponential backoff
        - Error handling and logging
        - Request/response formatting
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint path or full URL
            data: Request body data (for POST/PUT requests)
            params: Query parameters
            
        Returns:
            Parsed JSON response from the API
            
        Raises:
            RuntimeError: If client is not initialized
            httpx.HTTPStatusError: For HTTP error responses
            httpx.RequestError: For network/connection errors
        """
        if not self._client:
            raise RuntimeError("Client not initialized. Use async context manager.")
        
        headers = await self._get_headers()
        url = endpoint if endpoint.startswith('http') else f"{self.config.api_base_url}{endpoint}"
        
        for attempt in range(self.config.max_retries):
            try:
                logger.debug(f"Making {method} request to {url} (attempt {attempt + 1})")
                
                response = await self._client.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=data,
                    params=params
                )
                
                response.raise_for_status()
                result = response.json()
                
                logger.debug(f"Request successful: {method} {url}")
                return result
                
            except httpx.HTTPStatusError as e:
                logger.warning(f"HTTP error {e.response.status_code} for {method} {url}")
                
                # Handle authentication errors by refreshing token
                if e.response.status_code == 401:
                    logger.info("Access token expired, refreshing...")
                    await self.token_manager.refresh_token()
                    headers = await self._get_headers()
                    
                    # Retry with new token (don't count as retry attempt)
                    if attempt == 0:
                        continue
                
                # Don't retry client errors (4xx) except 401
                if 400 <= e.response.status_code < 500 and e.response.status_code != 401:
                    raise
                
                # Retry server errors (5xx) and other retryable errors
                if attempt == self.config.max_retries - 1:
                    raise
                
                # Exponential backoff
                wait_time = (2 ** attempt) * self.config.retry_delay
                logger.info(f"Retrying in {wait_time}s...")
                await asyncio.sleep(wait_time)
                
            except httpx.RequestError as e:
                logger.warning(f"Request error for {method} {url}: {e}")
                
                if attempt == self.config.max_retries - 1:
                    raise
                
                # Exponential backoff
                wait_time = (2 ** attempt) * self.config.retry_delay
                logger.info(f"Retrying in {wait_time}s...")
                await asyncio.sleep(wait_time)
        
        # This should never be reached due to the raise statements above
        raise RuntimeError("Max retries exceeded")
