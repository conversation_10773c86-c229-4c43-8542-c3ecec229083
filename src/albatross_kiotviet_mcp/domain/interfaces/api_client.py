"""API client interface."""

from abc import ABC, abstractmethod
# Import entities for type hints
from ..entities.category import CategoryResponse


class IKiotVietAPIClient(ABC):
    """Interface for KiotViet API client."""
    
    @abstractmethod
    async def __aenter__(self):
        """Async context manager entry."""
        pass
    
    @abstractmethod
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        pass
    
    @abstractmethod
    async def get_categories(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        hierarchical_data: bool = False
    ) -> CategoryResponse:
        """Get product categories from KiotViet API.

        Args:
            page_size: Number of items per page (max 100)
            current_item: Starting item index for pagination
            order_direction: Sort order ("Asc" or "Desc")
            hierarchical_data: Whether to return hierarchical structure

        Returns:
            Validated CategoryResponse with typed category data
        """
        pass
    

